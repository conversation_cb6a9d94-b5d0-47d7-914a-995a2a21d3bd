// Dart imports:
import 'dart:convert';

// Project imports:
import 'sticker_social.dart';

class StickerSetSocial {
  StickerSetSocial({this.items});

  final List<StickerSetSocialItems>? items;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'items': items?.map((final item) => item.toMap()).toList(),
    };
  }

  String toJson() => json.encode(toMap());
}

class StickerSetSocialItems {
  StickerSetSocialItems({
    this.id,
    this.name,
    this.createdBy,
    this.isFavorite,
    this.stickers,
    this.category,
  });

  final String? id;
  final String? name;
  final String? category;
  final String? createdBy;
  final bool? isFavorite;
  final List<StickerSocialItems>? stickers;

  StickerSetSocialItems copyWith({
    final String? id,
    final String? name,
    final String? category,
    final String? createdBy,
    final bool? isFavorite,
    final List<StickerSocialItems>? stickers,
  }) {
    return StickerSetSocialItems(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      createdBy: createdBy ?? this.createdBy,
      isFavorite: isFavorite ?? this.isFavorite,
      stickers: stickers ?? this.stickers,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'category': category,
      'createdBy': createdBy,
      'isFavorite': isFavorite,
      'stickers': stickers?.map((final sticker) => sticker.toMap()).toList(),
    };
  }

  String toJson() => json.encode(toMap());
}
