// Dart imports:
import 'dart:convert';

class StickerSocial {
  StickerSocial({this.item});

  final List<StickerSocialItems>? item;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'item': item?.map((final sticker) => sticker.toMap()).toList(),
    };
  }

  String toJson() => json.encode(toMap());
}

class StickerSocialItems {
  StickerSocialItems({
    this.id,
    this.icon,
    this.link,
    this.mimetype,
    this.setId,
    this.createdBy,
  });

  final String? id;
  final String? icon;
  final String? link;
  final String? mimetype;
  final String? setId;
  final String? createdBy;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'icon': icon,
      'link': link,
      'mimetype': mimetype,
      'setId': setId,
      'createdBy': createdBy,
    };
  }

  String toJson() => json.encode(toMap());
}
