// Dart imports:
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

// Flutter imports:
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports:
import 'package:animations/animations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:extended_image/extended_image.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:glass/glass.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pro_image_editor/designs/frosted_glass/frosted_glass.dart';
import 'package:pro_image_editor/pro_image_editor.dart';
import 'package:pro_image_editor/shared/widgets/layer/interaction_helper/layer_interaction_border_painter.dart';

// Project imports:
import '../../../core/nd_constants/strings.dart';
import '../../../core/nd_intl/nd_intl.dart';
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/params/alert_params.dart';
import '../../../core/params/sticker_request_params.dart';
import '../../../core/params/sticker_set_request_params.dart';
import '../../../core/params/sticker_set_update_request_params.dart';
import '../../../core/params/sticker_update_request_params.dart';
import '../../../core/utils/nd_utils.dart';
import '../../../domain/entities/social_upload_file.dart';
import '../../../domain/entities/sticker_set.dart';
import '../../../domain/entities/sticker_set_created_success.dart';
import '../../../domain/entities/sticker_social.dart';
import '../../../injector/injector.dart';
import '../../account/widgets/account_field.dart';
import '../../widgets/value_notifier_list.dart';
import '../bloc/sticker_bloc.dart';
import '../widgets/story_snapple.dart';
import 'story_preview_image_write_page.dart';

class StickerSave {
  StickerSave(this.params, this.type);

  final StickerRequestParams params;
  final StikerTypePop type;
}

enum StikerTypePop { selected, saveToLike, saveToSet }

@RoutePage()
class StoryEditImagePage extends StatefulWidget {
  const StoryEditImagePage({
    super.key,
    required this.file,
    required this.i,
    this.isUpdate = false,
  });
  final bool isUpdate;
  final File file;
  final int i;

  @override
  State<StoryEditImagePage> createState() => _StoryEditImagePageState();
}

class _StoryEditImagePageState extends State<StoryEditImagePage> {
  final GlobalKey<SnappableState> _snappableKey = GlobalKey<SnappableState>();
  final editorKey = GlobalKey<ProImageEditorState>();
  StickerSetRequestParams params = const StickerSetRequestParams(page: 1);
  Uint8List? editedBytes;
  double? _generationTime;
  DateTime? startEditingTime;
  Future<void> onImageEditingStarted() async {
    startEditingTime = DateTime.now();
  }

  Future<void> onImageEditingComplete(
    final Uint8List bytes, {
    final bool showThumbnail = false,
    final ui.Image? rawOriginalImage,
    required final int index,
  }) async {
    editedBytes = bytes;
    if (editedBytes != null) {
      await precacheImage(MemoryImage(editedBytes!), context);
      if (!mounted) {
        return;
      }
      editorKey.currentState?.isPopScopeDisabled = true;
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (final context) {
            return PreviewImgPage(
              imgBytes: editedBytes ?? Uint8List(0),
              generationTime: _generationTime,
              showThumbnail: showThumbnail,
              rawOriginalImage: rawOriginalImage,
              index: widget.i,
              isUpdate: widget.isUpdate,
            );
          },
        ),
      ).whenComplete(() {
        editedBytes = null;
        _generationTime = null;
        startEditingTime = null;
      });
    }
    setGenerationTime();
  }

  Future<void> onCloseEditor(final EditorMode mode) async {
    if (mounted) {
      Navigator.pop(context);
    }
  }

  void setGenerationTime() {
    if (startEditingTime != null) {
      _generationTime = DateTime.now()
          .difference(startEditingTime!)
          .inMilliseconds
          .toDouble();
    }
  }

  dynamic mapEmoji = {};
  List<String> data = [];
  final ValueNotifierList<StickerSocialItems> sticker = ValueNotifierList([]);
  final ValueNotifierList<StickerSetSocialItems> sets = ValueNotifierList([]);
  Future<void> _fetchEmoji() async {
    final String repo = await rootBundle.loadString('assets/emoji/emoji.json');
    mapEmoji = await jsonDecode(repo);
    // ignore: avoid_dynamic_calls
    data = [...(mapEmoji['emoji'] as List).map((final e) => e)];
    setState(() {});
  }

  @override
  void initState() {
    super.initState();

    unawaited(_fetchEmoji());
  }

  @override
  Widget build(final BuildContext context) {
    return ProImageEditor.file(
      widget.file,
      callbacks: ProImageEditorCallbacks(
        onImageEditingStarted: onImageEditingStarted,
        onImageEditingComplete: (final unit8List) async {
          return onImageEditingComplete(unit8List, index: widget.i);
        },
        onCloseEditor: onCloseEditor,
      ),
      configs: ProImageEditorConfigs(
        i18n: const I18n(
          paintEditor: I18nPaintEditor(bottomNavigationBarText: Strings.empty),
          textEditor: I18nTextEditor(bottomNavigationBarText: Strings.empty),
          cropRotateEditor: I18nCropRotateEditor(
            bottomNavigationBarText: Strings.empty,
          ),
          stickerEditor: I18nStickerEditor(
            bottomNavigationBarText: Strings.empty,
          ),
          filterEditor: I18nFilterEditor(
            bottomNavigationBarText: Strings.empty,
          ),
          blurEditor: I18nBlurEditor(bottomNavigationBarText: Strings.empty),
        ),
        designMode: ImageEditorDesignMode.cupertino,
        layerInteraction: LayerInteractionConfigs(
          widgets: LayerInteractionWidgets(
            border: (final layerWidget, final layerData) => Container(
              margin: const EdgeInsets.all(10),
              child: CustomPaint(
                foregroundPainter: LayerInteractionBorderPainter(
                  style: const LayerInteractionStyle(),
                ),
                child: layerWidget,
              ),
            ),
          ),
        ),
        paintEditor: PaintEditorConfigs(
          widgets: PaintEditorWidgets(
            appBar: (final paintEditor, final rebuildStream) => ReactiveAppbar(
              builder: (final _) => _appBarPaintingEditor(paintEditor),
              stream: rebuildStream,
            ),
            colorPicker:
                (
                  final paintEditor,
                  final rebuildStream,
                  final currentColor,
                  final setColor,
                ) => ReactiveWidget(
                  stream: rebuildStream,
                  builder: (final cxt) =>
                      FrostedGlassPaintBottomBar(paintEditor: paintEditor),
                ),
          ),
        ),
        stickerEditor: StickerEditorConfigs(
          enabled: true,
          builder: (final setLayer, final scrollController) {
            return ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
              child: BlocProvider(
                create: (final context) =>
                    getIt<StickerBloc>()..add(StickerSetGet(params)),
                // ..add(StickerRecentGet()),
                child: BlocConsumer<StickerBloc, StickerState>(
                  listener: (final context, final state) {
                    if (state.status == StickerStatus.updateSetSuccess) {
                      final StickerSetSocialItems data = Utils.getData(
                        state.data,
                      );
                      final idxSet = sets.value.indexWhere(
                        (final e) => e.id == data.id,
                      );
                      if (idxSet != -1) {
                        final getSet = sets.value[idxSet].copyWith(
                          name: data.name,
                        );
                        sets.updateValuebyIndex(idxSet, getSet);
                      }
                    }
                    if (state.status == StickerStatus.removeSetSuccess) {
                      final setId = state.data.toString();
                      final indexW = sets.value.indexWhere(
                        (final e) => e.id == setId,
                      );
                      sets.removeIndex(indexW);
                    }
                    if (state.status == StickerStatus.setSuccess) {
                      final StickerSetSocial set = Utils.getData(state.data);
                      final listSafe = set.items?.map((final e) => e).toList();
                      sets.setValue(listSafe.first);
                    }

                    // if (state.status == StickerStatus.recentSuccess) {
                    //   final StickerSocial data = Utils.getData(state.data);
                    //   final listSafe = data.item
                    //       ?.map(
                    //         (final e) => e,
                    //       )
                    //       .toList();
                    //   sticker.setValue(listSafe);
                    // }
                    if (state.status == StickerStatus.updateSuccess) {
                      final StickerSocialItems sticker = Utils.getData(
                        state.data,
                      );
                      if (state.type == StikerTypePop.selected) {
                        final item = ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: SizedBox(
                            width: 60,
                            height: 60,
                            child: CachedNetworkImage(
                              imageUrl: sticker.link ?? '',
                            ),
                          ),
                        );
                        final layer = WidgetLayer(widget: item);
                        setLayer(layer);
                      }
                    }
                    if (state.status == StickerStatus.createSuccess) {
                      final StickerSetCreatedSuccess data = Utils.getData(
                        state.data,
                      );

                      if (state.type == StikerTypePop.selected) {
                        context.read<StickerBloc>().add(
                          StickerRecentUpdate(
                            StickerUpdateRequestParams(
                              setId: data.sticker?.setId,
                              id: data.sticker?.id,
                            ),
                            type: state.type,
                          ),
                        );
                      } else {
                        final iSet = sets.value.indexWhere(
                          (final e) => e.id == data.set?.id,
                        );
                        if (iSet != -1) {
                          final item = sets.value[iSet];
                          item.stickers?.add(
                            data.sticker ?? StickerSocialItems(),
                          );
                          sets.updateValuebyIndex(iSet, item);
                        } else {
                          final newSet = StickerSetSocialItems(
                            id: data.set?.id,
                            name: data.set?.name,
                            createdBy: data.set?.createdBy,
                            stickers: [data.sticker ?? StickerSocialItems()],
                          );
                          sets.add(newSet);
                        }
                      }
                    }
                    if (state.status == StickerStatus.uploadSuccess) {
                      final SocialUploadFile att = Utils.getData(state.data);
                      context.read<StickerBloc>().add(
                        StickerCreate(
                          params:
                              state.params?.copyWith(url: att.link) ??
                              const StickerRequestParams(),
                          type: state.type,
                        ),
                      );
                    }
                  },
                  builder: (final context, final state) {
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSticker(),
                          ValueListenableBuilder(
                            valueListenable: sets,
                            builder: (final context, final vSets, final child) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ...List.generate(vSets.length, (final i) {
                                    final item = vSets[i];
                                    return _buildItemSet(item, context,
                                     setLayer);
                                  }),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            );
          },
        ),
        textEditor: TextEditorConfigs(
          showSelectFontStyleBottomBar: true,
          customTextStyles: [
            GoogleFonts.roboto(),
            GoogleFonts.averiaLibre(),
            GoogleFonts.lato(),
            GoogleFonts.comicNeue(),
            GoogleFonts.actor(),
            GoogleFonts.odorMeanChey(),
            GoogleFonts.nabla(),
          ],
        ),
        emojiEditor: const EmojiEditorConfigs(enabled: false),
        tuneEditor: const TuneEditorConfigs(enabled: false),
        filterEditor: const FilterEditorConfigs(
          icons: FilterEditorIcons(bottomNavBar: Icons.wb_sunny),
        ),
        mainEditor: MainEditorConfigs(
          widgets: MainEditorWidgets(
            closeWarningDialog: (final state) async {
              return await showDialog<bool>(
                    context: context,
                    builder: (final BuildContext context) => AlertDialog(
                      content: Text(
                        context.l10n.imageEdit,
                        textAlign: TextAlign.center,
                      ),
                      actions: <Widget>[
                        DecoratedBox(
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                          ),
                          child: Center(
                            child: Row(
                              children: [
                                Expanded(
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () => Navigator.pop(context, false),
                                    child: Center(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 24,
                                        ),
                                        child: Text(
                                          context.l10n.cancel,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () => Navigator.pop(context, true),
                                    child: Center(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 10,
                                          horizontal: 24,
                                        ),
                                        child: Text(
                                          context.l10n.confirm,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: Theme.of(
                                                  context,
                                                ).primaryColor,
                                              ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ) ??
                  false;
            },
            removeLayerArea: (final key, final editor, final rebuildStream,
            final  isRemove) {
              return Positioned(
                key: key,
                bottom: 50,
                left: MediaQuery.sizeOf(context).width / 2 - 30,
                right: MediaQuery.sizeOf(context).width / 2 - 30,
                child: SafeArea(
                  bottom: false,
                  child: StreamBuilder(
                    stream: rebuildStream,
                    builder: (final context, final snapshot) {
                      if (editor.layerInteractionManager.hoverRemoveBtn) {
                        HapticFeedback.mediumImpact();
                      }
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.fastOutSlowIn,
                        height: editor.layerInteractionManager.hoverRemoveBtn
                            ? 72
                            : 56,
                        width: editor.layerInteractionManager.hoverRemoveBtn
                            ? 72
                            : 56,
                        decoration: BoxDecoration(
                          color: editor.layerInteractionManager.hoverRemoveBtn
                              ? Colors.red
                              : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                        child: const Center(
                          child: Icon(Icons.delete_outline, size: 28),
                        ),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  ValueListenableBuilder<List<StickerSocialItems>> _buildSticker() {
    return ValueListenableBuilder(
      valueListenable: sticker,
      builder: (final context, final vSticker, final child) {
        return Wrap(
          spacing: 10,
          runSpacing: 10,
          children: [
            GestureDetector(
              onTap: () async {
                final ValueNotifier<File?> imageFile = ValueNotifier(null);
                final controller = CustomImageCropController();
                ImagePicker().pickImage(source: ImageSource.gallery).then((
                  final val,
                ) {
                  if (val != null) {
                    if (context.mounted) {
                      imageFile.value = File(val.path);
                      _showDropSticker(context, imageFile, controller).then((
                        final val,
                      ) {
                        final isStickerType = val != null && val is StickerSave;
                        if (isStickerType) {
                          if (context.mounted) {
                            if (val.type == StikerTypePop.selected) {
                              context.read<StickerBloc>().add(
                                StickerUploadFile(
                                  [File(val.params.url ?? '')],
                                  val.params,
                                  val.type,
                                ),
                              );
                            } else {
                              context.read<StickerBloc>().add(
                                StickerUploadFile(
                                  [File(val.params.url ?? '')],
                                  val.params,
                                  val.type,
                                ),
                              );
                            }
                          }
                        }
                      });
                    }
                  }
                });
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: const SizedBox(
                  width: 60,
                  height: 60,
                  child: Icon(Icons.add),
                ),
              ),
            ),
            ...List.generate(vSticker.length, (final i) {
              final item = ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: SizedBox(
                  width: 60,
                  height: 60,
                  child: ExtendedImage.network(vSticker[i].link ?? ''),
                ),
              );
              return GestureDetector(
                onTap: () {
                  context.read<StickerBloc>().add(
                    StickerRecentUpdate(
                      StickerUpdateRequestParams(
                        id: vSticker[i].id,
                        setId: vSticker[i].setId,
                      ),
                      type: StikerTypePop.selected,
                    ),
                  );
                },
                child: item,
              );
            }),
          ],
        );
      },
    );
  }

  Padding _buildItemSet(
    final StickerSetSocialItems item,
    final BuildContext context,
    final Function(WidgetLayer) setLayer
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(item.name ?? ''),
          if ((item.stickers ?? []).isNotEmpty)
            Wrap(
              runAlignment: WrapAlignment.center,
              children: [
                ...List.generate(item.stickers!.length, (final i2) {
                  final item2 = item.stickers?[i2];
                  final sticker = ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: SizedBox(
                      width: 60,
                      height: 60,
                      child: CachedNetworkImage(
                        imageUrl: item2?.link ?? '',
                        placeholder: (final context, final url) =>
                            const LoadingWidget(),
                        errorWidget: (final context, final url, final error) =>
                            const Icon(Icons.error),
                      ),
                    ),
                  );
                  return Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: GestureDetector(
                      onTap: () {
                        setLayer(
                          WidgetLayer(widget: sticker),
                        );
                      },
                      child: sticker,
                    ),
                  );
                }),
              ],
            )
          else
            const SizedBox(),
        ],
      ),
    );
  }

  Future<dynamic> _showDropSticker(
    final BuildContext context,
    final ValueNotifier<File?> imageFile,
    final CustomImageCropController controller,
  ) {
    return showModal(
      context: context,
      builder: (final context) => AlertDialog(
        actionsAlignment: MainAxisAlignment.center,
        titlePadding: EdgeInsets.zero,
        insetPadding: EdgeInsets.zero,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        title: _buildHeader(context, context.l10n.createStickers),
        content: SizedBox(
          width: MediaQuery.sizeOf(context).width,
          height: MediaQuery.sizeOf(context).height / 3,
          child: Stack(
            children: [
              ValueListenableBuilder(
                valueListenable: imageFile,
                builder: (final context, final vImageFile, final child) {
                  return vImageFile == null
                      ? const SizedBox()
                      : Stack(
                          children: [
                            Positioned.fill(
                              child: DottedBorder(
                                color: Theme.of(context).primaryColor,
                                dashPattern: const [3, 9],
                                strokeWidth: 4,
                                strokeCap: StrokeCap.round,
                                borderType: BorderType.RRect,
                                radius: const Radius.circular(5),
                                child: const SizedBox(),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(2.0),
                              child: Snappable(
                                key: _snappableKey,
                                child: CustomImageCrop(
                                  borderRadius: 5,
                                  cropPercentage: 1.24,
                                  outlineColor: Colors.white.withValues(
                                    alpha: 0,
                                  ),
                                  shape: CustomCropShape.Square,
                                  cropController: controller,
                                  image: FileImage(vImageFile, scale: .9),
                                ),
                              ),
                            ),
                          ],
                        );
                },
              ),
            ],
          ),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              final state = _snappableKey.currentState!;
              if (state.isGone) {
                state.reset();
              } else {
                state.snap();
              }
            },
            child: const Text('snap'),
          ),
          const SizedBox(height: 4),
          ElevatedButton(
            onPressed: () {
              final ValueNotifier<bool> isOpen = ValueNotifier(false);
              _openSticker(context, isOpen).then((final val) {
                final isStickerType = val != null && val is StickerSave;
                if (isStickerType) {
                  if (context.mounted) {
                    if (val.type == StikerTypePop.selected) {
                      controller.addTransition(CropImageData(angle: -pi / 1));
                      controller.onCropImage().then((final data) async {
                        final tempDir = await getTemporaryDirectory();
                        final time = DateTime.now().formatDate3();
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(data?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.selected,
                                  ),
                                );
                              }
                            });
                      });
                    }
                    if (val.type == StikerTypePop.saveToLike) {
                      controller.addTransition(CropImageData(angle: -pi / 1));
                      controller.onCropImage().then((final data) async {
                        final tempDir = await getTemporaryDirectory();
                        final time = DateTime.now().formatDate3();
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(data?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.saveToLike,
                                  ),
                                );
                              }
                            });
                      });
                    }
                    if (val.type == StikerTypePop.saveToSet) {
                      controller.addTransition(CropImageData(angle: -pi / 1));
                      controller.onCropImage().then((final data) async {
                        final tempDir = await getTemporaryDirectory();
                        final time = DateTime.now().formatDate3();
                        File('${tempDir.path}/$time-image-temp.png')
                            .writeAsBytes(data?.bytes ?? Uint8List(0))
                            .then((final file) {
                              if (context.mounted) {
                                context.router.popForced(
                                  StickerSave(
                                    val.params.copyWith(url: file.path),
                                    StikerTypePop.saveToSet,
                                  ),
                                );
                              }
                            });
                      });
                    }
                  }
                }
              });
            },
            child: const Text('snap'),
          ),
        ],
      ),
    );
  }

  Future<dynamic> _openSticker(
    final BuildContext context,
    final ValueNotifier<bool> isOpen,
  ) {
    return showModal(
      context: context,
      builder: (final builder) {
        return GestureDetector(
          onTap: () {
            context.router.popForced();
          },
          child: AlertDialog.adaptive(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(0),
            ),
            backgroundColor: Colors.transparent,
            insetPadding: EdgeInsets.zero,
            contentPadding: EdgeInsets.zero,
            clipBehavior: Clip.antiAliasWithSaveLayer,
            content: SizedBox.expand(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      width: MediaQuery.sizeOf(context).width / 2,
                      height: MediaQuery.sizeOf(context).width / 2,
                    ),
                    const SizedBox(height: 12),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Stack(
                        children: [
                          SizedBox(
                            child: Column(
                              children: [
                                DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      _buildSelectedSticker(context),
                                      _buildFavoriteSticker(context),
                                      _buildSaveSetSticker(isOpen),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 150),
                              ],
                            ),
                          ),
                          ValueListenableBuilder(
                            valueListenable: isOpen,
                            builder:
                                (final context, final vIsOpen, final child) {
                                  return AnimatedSize(
                                    duration: const Duration(milliseconds: 200),
                                    child: vIsOpen
                                        ? _buildNewSet(isOpen, context)
                                        : const SizedBox(),
                                  );
                                },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ).asGlass(),
        );
      },
    );
  }

  ColoredBox _buildNewSet(
    final ValueNotifier<bool> isOpen,
    final BuildContext context,
  ) {
    return ColoredBox(
      color: Colors.white,
      child: SizedBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            IconButton(
              onPressed: () async {
                isOpen.value = false;
              },
              icon: EZResources.image(ImageParams(name: AppIcons.icBack)),
            ),
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('them muc moi'),
              onTap: () {
                final TextEditingController name = TextEditingController(
                  text: '',
                );
                showModal(
                  context: context,
                  builder: (final builder) {
                    return AlertDialog.adaptive(
                      title: const Text('tesst tao sticker'),
                      content: AccountField(
                        controller: name,
                        isOnlyReady: false,
                        label: '',
                        hintText: '',
                      ),
                      actions: [
                        TextButton(onPressed: () {}, child: const Text('huy')),
                        TextButton(
                          onPressed: () {
                            context.router.popForced();
                            context.router.popForced(
                              StickerSave(
                                StickerRequestParams(
                                  setName: name.text,
                                  label: 'tesstSet',
                                  url: '',
                                ),
                                StikerTypePop.saveToSet,
                              ),
                            );
                          },
                          child: const Text('ok'),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            SizedBox(
              width: MediaQuery.sizeOf(context).width / 2,
              height: 120,
              child: ListView.separated(
                itemBuilder: (final _, final i) {
                  final item = sets.value[i];
                  return ListTile(
                    onTap: () {
                      context.router.popForced(
                        StickerSave(
                          StickerRequestParams(
                            setId: item.id,
                            label: 'tesstSet',
                            url: '',
                          ),
                          StikerTypePop.saveToSet,
                        ),
                      );
                      // context.router.popForced(
                      //   _StickerSave(
                      //   StickerRequestParams(
                      //       label: 'tesstt123',
                      //       url: '',
                      //       setId: item.id,
                      //     ),
                      //     StikerTypePop.saveToSet,
                      // ),);
                    },
                    title: Text(item.name ?? ''),
                  );
                },
                separatorBuilder: (final _, final i) => const Divider(),
                itemCount: sets.value.length,
              ),
            ),
          ],
        ),
      ),
    );
  }

  ListTile _buildSaveSetSticker(final ValueNotifier<bool> isOpen) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('lưu vào mục cua ban'),
      onTap: () {
        isOpen.value = true;
      },
    );
  }

  ListTile _buildFavoriteSticker(final BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('lưu vào mục yêu thích'),
      onTap: () {
        context.router.popForced(
          StickerSave(
            const StickerRequestParams(
              setId: 'FAVORITE',
              label: 'tesst12',
              url: '',
            ),
            StikerTypePop.saveToLike,
          ),
        );
      },
    );
  }

  ListTile _buildSelectedSticker(final BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.abc),
      title: const Text('chọn luôn'),
      onTap: () {
        context.router.popForced(
          StickerSave(
            const StickerRequestParams(
              setId: 'ANONYMOUS',
              label: 'tesst',
              url: '',
            ),
            StikerTypePop.selected,
          ),
        );
      },
    );
  }

  AppBar _appBarPaintingEditor(final PaintEditorState paintEditor) {
    return AppBar(
      centerTitle: true,
      automaticallyImplyLeading: false,
      foregroundColor: Colors.white,
      backgroundColor: Colors.black,
      leading: IconButton(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        icon: const Icon(Icons.arrow_back),
        onPressed: paintEditor.close,
      ),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            icon: const Icon(Icons.line_weight_rounded, color: Colors.white),
            onPressed: paintEditor.openLinWidthBottomSheet,
          ),
          IconButton(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            icon: Icon(
              paintEditor.fillBackground == true
                  ? Icons.format_color_reset
                  : Icons.format_color_fill,
              color: Colors.white,
            ),
            onPressed: paintEditor.toggleFill,
          ),
        ],
      ),
      actions: [
        IconButton(
          tooltip: 'Done',
          padding: const EdgeInsets.symmetric(horizontal: 8),
          icon: const Icon(Icons.done),
          iconSize: 28,
          onPressed: paintEditor.done,
        ),
      ],
    );
  }

  Widget _buildHeader(final BuildContext context, final String title) {
    return ColoredBox(
      color: const Color(0xffF6F7FB),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: IconButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                },
                icon: EZResources.image(ImageParams(name: AppIcons.icCancel)),
              ),
            ),
            Align(
              child: Text(title, style: Theme.of(context).textTheme.titleSmall),
            ),
          ],
        ),
      ),
    );
  }
}
